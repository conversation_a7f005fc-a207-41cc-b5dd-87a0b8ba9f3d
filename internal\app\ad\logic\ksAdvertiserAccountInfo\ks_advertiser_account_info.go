// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-13 15:02:27
// 生成路径: internal/app/ad/logic/ks_advertiser_account_info.go
// 生成人：cyao
// desc:快手广告账户表格
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"sort"
)

func init() {
	service.RegisterKsAdvertiserAccountInfo(New())
}

func New() service.IKsAdvertiserAccountInfo {
	return &sKsAdvertiserAccountInfo{}
}

type sKsAdvertiserAccountInfo struct{}

func (s *sKsAdvertiserAccountInfo) List(ctx context.Context, req *model.KsAdvertiserAccountInfoSearchReq) (listRes *model.KsAdvertiserAccountInfoSearchRes, err error) {
	listRes = new(model.KsAdvertiserAccountInfoSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {

		userInfo := sysService.Context().GetLoginUser(ctx)
		_, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.KsAdvertiserAccountInfo.Ctx(ctx).WithAll()
		if !admin {
			m = m.Where(dao.KsAdvertiserAccountInfo.Columns().Owner, userInfo.Id)
		}

		if req.KeyWord != "" {
			m = m.Where(dao.KsAdvertiserAccountInfo.Columns().AccountName+" like ?", "%"+req.KeyWord+"%")
		}
		if req.AccountId > 0 {
			m = m.Where(dao.KsAdvertiserAccountInfo.Columns().AccountId+" = ?", req.AccountId)
		}
		if len(req.AccountIds) > 0 {
			m = m.WhereIn(dao.KsAdvertiserAccountInfo.Columns().AccountId, req.AccountIds)
		}
		if len(req.AccountNames) > 0 {
			m = m.WhereIn(dao.KsAdvertiserAccountInfo.Columns().AccountName, req.AccountNames)
		}

		if req.CorporationName != "" {
			m = m.Where(dao.KsAdvertiserAccountInfo.Columns().CorporationName+" like ?", "%"+req.CorporationName+"%")
		}
		if req.AuthSource != "" {
			m = m.Where(dao.KsAdvertiserAccountInfo.Columns().AuthSource+" = ?", req.AuthSource)
		}
		if req.AuthStatus != "" {
			m = m.Where(dao.KsAdvertiserAccountInfo.Columns().AuthStatus+" = ?", gconv.Int(req.AuthStatus))
		}
		if req.DeliveryStatus != "" {
			m = m.Where(dao.KsAdvertiserAccountInfo.Columns().DeliveryStatus+" = ?", gconv.Int(req.DeliveryStatus))
		}
		if req.BalanceStatus > 0 {
			if req.BalanceStatus == 1 {
				m = m.Where(dao.KsAdvertiserAccountInfo.Columns().Balance + " > 0")
			} else {
				m = m.Where(dao.KsAdvertiserAccountInfo.Columns().Balance + " <= 0")
			}
		}

		if req.Owner != "" {
			m = m.Where(dao.KsAdvertiserAccountInfo.Columns().Owner+" = ?", gconv.Int(req.Owner))
		}
		if req.DeliveryStatus != "" {
			m = m.Where(dao.KsAdvertiserAccountInfo.Columns().DeliveryStatus+" = ?", gconv.Int(req.DeliveryStatus))
		}
		if req.StartTime != "" {
			m = m.Where(dao.KsAdvertiserAccountInfo.Columns().CreateTime+" >= ?", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.Where(dao.KsAdvertiserAccountInfo.Columns().CreateTime+" <= ?", libUtils.StringTimeAddDay(req.EndTime, 1))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "account_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserAccountInfoListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserAccountInfoListRes, len(res))
		userIds := make([]uint64, len(res))
		for k, v := range res {
			userIds = append(userIds, gconv.Uint64(v.Owner))
			listRes.List[k] = &model.KsAdvertiserAccountInfoListRes{
				AccountId:          v.AccountId,
				AgentAccountId:     v.AgentAccountId,
				AuthorizeKsAccount: v.AuthorizeKsAccount,
				UserId:             v.UserId,
				AccountName:        v.AccountName,
				Balance:            v.Balance,
				TotalBalance:       v.TotalBalance,
				DayBudget:          v.DayBudget,
				Remark:             v.Remark,
				AuthSource:         v.AuthSource,
				AuthStatus:         v.AuthStatus,
				DeliveryStatus:     v.DeliveryStatus,
				Owner:              v.Owner,
				CreateTime:         v.CreateTime,
			}
		}

		userList, _ := sysService.SysUser().GetUserByIds(ctx, userIds)
		for k, v := range listRes.List {
			for _, user := range userList {
				if v.Owner == int(user.Id) {
					listRes.List[k].OwnerUserName = user.UserName
				}
			}
		}
	})
	return
}

// BatchSetAccountOwner
func (s *sKsAdvertiserAccountInfo) BatchSetAccountOwner(ctx context.Context, req *model.KsAdvertiserAccountInfoSetOwnerReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var list []*model.KsAdvertiserAccountInfoInfoRes
		err = dao.KsAdvertiserAccountInfo.Ctx(ctx).WithAll().WhereIn(dao.KsAdvertiserAccountInfo.Columns().AccountId, req.AccountIds).Scan(&list)
		liberr.ErrIsNil(ctx, err, "查询数据失败: "+err.Error())
		for _, item := range list {
			item.Owner = req.Owner
		}
		_, err = dao.KsAdvertiserAccountInfo.Ctx(ctx).Save(list)
		liberr.ErrIsNil(ctx, err, "批量设置账户归属人员失败: "+err.Error())
	})
	return
}

// UpLoadVideo
func (s *sKsAdvertiserAccountInfo) UpLoadVideo(ctx context.Context, req *model.UpLoadVideoReq) (listRes *model.UpLoadVideoRes, err error) {
	listRes = new(model.UpLoadVideoRes)
	err = g.Try(ctx, func(ctx context.Context) {
		// 获取token
		var accessToken = ""
		accessToken, err = s.GetAccessToken(ctx, req.AdvertiserId)
		liberr.ErrIsNil(ctx, err, "获取token失败: ")
		//首先获取上传的token
		generateTokenRes, err2 := ksApi.GetKSApiClient().UploadTokenGenerateService.AccessToken(accessToken).
			SetReq(ksApi.UploadTokenGenerateReq{AdvertiserId: gconv.Int64(req.AdvertiserId), FileType: "mp4"}).
			Do()
		liberr.ErrIsNil(ctx, err2, "获取上传的token失败")
		//文件流分片上传 PostAdVideoUploadService
		uploadRes, err2 := ksApi.GetKSApiClient().PostAdVideoUploadService.AccessToken(accessToken).
			SetReq(ksApi.PostAdVideoUploadReq{
				AdvertiserId: gconv.Int64(req.AdvertiserId),
				FileUrl:      req.FileUrl,
				UploadToken:  generateTokenRes.Data.UploadToken,
				Endpoint:     generateTokenRes.Data.Endpoint,
			}).Do()
		liberr.ErrIsNil(ctx, err2, "文件流分片上传失败")
		//领用上传token FileAdVideoUploadService
		uploadRes2, err2 := ksApi.GetKSApiClient().FileAdVideoUploadService.AccessToken(accessToken).
			SetReq(ksApi.FileAdVideoUploadReq{
				AdvertiserId: gconv.Int64(req.AdvertiserId),
				BlobStoreKey: uploadRes.Data.BlobStoreKey,
				Signature:    uuid.NewString(),
			}).Do()
		liberr.ErrIsNil(ctx, err2, "上传视频文件失败")
		// 上传视频文件 FileAdVideoUploadService
		//uploadRes3, err2 := ksApi.GetKSApiClient().FileAdVideoUploadService.AccessToken(accessToken).
		//	SetReq(ksApi.FileAdVideoUploadReq{
		//		AdvertiserId: gconv.Int64(req.AdvertiserId),
		//		BlobStoreKey: uploadRes.Data.BlobStoreKey,
		//		Signature:    uploadRes2.Data.Signature,
		//	}).Do()
		//liberr.ErrIsNil(ctx, err2, "上传视频文件失败")
		listRes.Signature = uploadRes2.Data.Signature
		listRes.PhotoId = uploadRes2.Data.PhotoId
	})
	return
}

// KsAdvertiserGetImportList
func (s *sKsAdvertiserAccountInfo) GetImportList(ctx context.Context, req *model.KsAdvertiserGetImportListReq) (listRes *model.KsAdvertiserGetImportListRes, err error) {
	listRes = new(model.KsAdvertiserGetImportListRes)
	err = g.Try(ctx, func(ctx context.Context) {

		libUtils.SafeGo(func() {
			innerCtx, cancel := context.WithCancel(context.Background())
			defer cancel()
			err = service.KsAdvertiserAccountInfo().PullByAgentId(innerCtx, gconv.Int64(req.AgentId), gconv.Int64(req.AuthorizeKsAccount))
			if err != nil {
				g.Log().Errorf(innerCtx, "KsAdCallBack2 PullByAgentId err:%+v", err)
			}
		})

		m := dao.KsAdvertiserAccountInfo.Ctx(ctx).WithAll()
		if req.AccountId != "" {
			m = m.Where(dao.KsAdvertiserAccountInfo.Columns().AccountId+" = ?", req.AccountId)
		}
		if req.AccountName != "" {
			m = m.WhereLike(dao.KsAdvertiserAccountInfo.Columns().AccountName, "%"+req.AccountName+"%")
		}
		if req.IsNoAuthor {
			m = m.Where(dao.KsAdvertiserAccountInfo.Columns().AuthStatus + " = 0")
		}
		if req.AgentId > 0 {
			m = m.Where(dao.KsAdvertiserAccountInfo.Columns().AgentAccountId+" = ?", gconv.Int64(req.AgentId))
		}
		if req.AuthorizeKsAccount > 0 {
			m = m.Where(dao.KsAdvertiserAccountInfo.Columns().AuthorizeKsAccount+" = ?", req.AuthorizeKsAccount)
		}
		if req.CorporationName != "" {
			m = m.WhereLike(dao.KsAdvertiserAccountInfo.Columns().CorporationName, req.CorporationName)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "account_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserGetImportRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserGetImportRes, len(res))
		userIds := make([]uint64, len(res))
		for k, v := range res {
			userIds = append(userIds, gconv.Uint64(v.Owner))
			listRes.List[k] = &model.KsAdvertiserGetImportRes{
				AccountId:          v.AccountId,
				AgentAccountId:     v.AgentAccountId,
				AuthorizeKsAccount: v.AuthorizeKsAccount,
				AccountName:        v.AccountName,
				CorporationName:    v.CorporationName,
				Owner:              v.Owner,
			}
		}

		userList, _ := sysService.SysUser().GetUserByIds(ctx, userIds)
		for k, v := range listRes.List {
			for _, user := range userList {
				if v.Owner == int64(user.Id) {
					listRes.List[k].OwnerUserName = user.UserName
				}
			}
		}

	})
	return
}

func (s *sKsAdvertiserAccountInfo) GetByAccountId(ctx context.Context, accountId int64) (res *model.KsAdvertiserAccountInfoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserAccountInfo.Ctx(ctx).WithAll().Where(dao.KsAdvertiserAccountInfo.Columns().AccountId, accountId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserAccountInfo) GetByAccountIds(ctx context.Context, accountIds []int64) (res []*model.KsAdvertiserAccountInfoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserAccountInfo.Ctx(ctx).WithAll().WhereIn(dao.KsAdvertiserAccountInfo.Columns().AccountId, accountIds).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

// PullByAgentId 根据代理商id 获取广告账户数据 UserId 快手授权账号
func (s *sKsAdvertiserAccountInfo) PullByAgentId(ctx context.Context, agentId, ksUserId int64) (err error) {
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	err = g.Try(innerCtx, func(innerCtx context.Context) {
		// 从缓存中获取 当前agent拉取的最新的AccountId
		accountId := 0
		var cmdStr = commonService.GetGoRedis().Get(innerCtx, ksApi.GetLastAccountIdKey(agentId, ksUserId))
		if cmdStr != nil {
			if gconv.Int(cmdStr.Val()) > 0 {
				accountId = gconv.Int(cmdStr.Val())
			}
		}
		agentInfo, _ := service.KsAdvertiserAgentInfo().GetAPPConfig(ctx, agentId, ksUserId)
		accessToken := ksApi.GetAccessTokenByAgentCache(agentId, ksUserId, gconv.Int64(agentInfo.AppId), agentInfo.Secret)
		for {
			fetchAccountList, err := ksApi.GetKSApiClient().GetFetchAccountListService.AccessToken(accessToken).SetReq(ksApi.GetFetchAccountListReq{
				AgentId:        agentId,
				BeginAccountId: accountId,
				BatchSize:      1000,
			}).Do()
			if err != nil {
				g.Log().Errorf(innerCtx, "拉取广告账号失败: %v", err)
				break
			}
			if fetchAccountList == nil || fetchAccountList.Data == nil || fetchAccountList.Data.TotalCount == 0 {
				break
			}

			var batchReq = make([]*model.KsAdvertiserAccountInfoAddReq, 0)
			for _, accountDetail := range fetchAccountList.Data.Details {
				batchReq = append(batchReq, &model.KsAdvertiserAccountInfoAddReq{
					AccountId:             accountDetail.AccountID,
					AgentAccountId:        agentId,
					AuthorizeKsAccount:    ksUserId,
					UserId:                accountDetail.UserID,
					AccountName:           accountDetail.AccountName,
					ResponsiblePerson:     accountDetail.ResponsiblePerson,
					UcType:                accountDetail.UCType,
					PaymentType:           accountDetail.PaymentType,
					Balance:               accountDetail.Balance,
					CreditBalance:         accountDetail.CreditBalance,
					ExtendedBalance:       accountDetail.ExtendedBalance,
					Rebate:                accountDetail.Rebate,
					PreRebate:             accountDetail.PreRebate,
					ContractRebate:        accountDetail.ContractRebate,
					TotalBalance:          accountDetail.TotalBalance,
					LoLimit:               accountDetail.LoLimit,
					SingleOut:             accountDetail.SingleOut,
					BalanceWarn:           libUtils.BoolParsInt(accountDetail.BalanceWarn),
					ProductName:           accountDetail.ProductName,
					FirstCostDay:          accountDetail.FirstCostDay,
					Industry:              accountDetail.Industry,
					SecondIndustry:        accountDetail.SecondIndustry,
					Recharged:             libUtils.BoolParsInt(accountDetail.Recharged),
					CorporationName:       accountDetail.CorporationName,
					ReviewStatus:          accountDetail.ReviewStatus,
					FrozenStatus:          accountDetail.FrozenStatus,
					TransferAccountStatus: libUtils.BoolParsInt(accountDetail.TransferAccountStatus),
					ChildReviewStatusInfo: libUtils.AnyMarshalToString(accountDetail.ChildReviewStatusInfo),
					CopyAccount:           libUtils.BoolParsInt(accountDetail.CopyAccount),
					ReviewDetail:          libUtils.AnyMarshalToString(accountDetail.ReviewDetails),
					DirectRebate:          accountDetail.DirectRebate,
					OptimizerOwner:        accountDetail.OptimizerOwner,
					//AutoOut:              ,
					AccountAutoManage: 0, // 具体导入账户的时候再获取
					DayBudget:         0, // 具体导入账户的时候再获取
					Remark:            "",
					AuthSource:        "1",
					AuthStatus:        0, // 默认导入进来的都是未授权的
					DeliveryStatus:    0,
					Owner:             0,
					CreateTime:        accountDetail.CreateTime,
				})
				accountId = int(accountDetail.AccountID)
			}

			err = s.BatchAdd(innerCtx, batchReq)
			if err != nil {
				g.Log().Errorf(innerCtx, "sKsAdvertiserAccountInfo 批量新增失败%v", err.Error())
			}
		}
	})
	return
}

// PullByAIds 拉取数据根据广告ids
func (s *sKsAdvertiserAccountInfo) PullByAIds(ctx context.Context, accountIds []int, agentId, ksUserId int64) (list []*model.KsAdvertiserAccountInfoAddReq, accountList []int, err error) {
	list = make([]*model.KsAdvertiserAccountInfoAddReq, 0)
	err = g.Try(ctx, func(ctx context.Context) {
		accountId := accountIds[0] - 1
		//获取accountIds 最后一个元素
		lastAccountId := accountIds[len(accountIds)-1]
		agentInfo, _ := service.KsAdvertiserAgentInfo().GetAPPConfig(ctx, agentId, ksUserId)
		accessToken := ksApi.GetAccessTokenByAgentCache(agentId, ksUserId, gconv.Int64(agentInfo.AppId), agentInfo.Secret)
		for {
			fetchAccountList, err := ksApi.GetKSApiClient().GetFetchAccountListService.AccessToken(accessToken).SetReq(ksApi.GetFetchAccountListReq{
				AgentId:        agentId,
				BeginAccountId: accountId,
				BatchSize:      1000,
			}).Do()
			if err != nil {
				g.Log().Errorf(ctx, "拉取广告账号失败: %v", err)
				break
			}
			if fetchAccountList == nil || fetchAccountList.Data == nil || fetchAccountList.Data.TotalCount == 0 {
				accountList = accountIds
				break
			}
			for _, accountDetail := range fetchAccountList.Data.Details {
				for i, id := range accountIds {
					if id == int(accountDetail.AccountID) {
						list = append(list, &model.KsAdvertiserAccountInfoAddReq{
							AccountId:             accountDetail.AccountID,
							AgentAccountId:        agentId,
							AuthorizeKsAccount:    ksUserId,
							UserId:                accountDetail.UserID,
							AccountName:           accountDetail.AccountName,
							ResponsiblePerson:     accountDetail.ResponsiblePerson,
							UcType:                accountDetail.UCType,
							PaymentType:           accountDetail.PaymentType,
							Balance:               accountDetail.Balance,
							CreditBalance:         accountDetail.CreditBalance,
							ExtendedBalance:       accountDetail.ExtendedBalance,
							Rebate:                accountDetail.Rebate,
							PreRebate:             accountDetail.PreRebate,
							ContractRebate:        accountDetail.ContractRebate,
							TotalBalance:          accountDetail.TotalBalance,
							LoLimit:               accountDetail.LoLimit,
							SingleOut:             accountDetail.SingleOut,
							BalanceWarn:           libUtils.BoolParsInt(accountDetail.BalanceWarn),
							ProductName:           accountDetail.ProductName,
							FirstCostDay:          accountDetail.FirstCostDay,
							Industry:              accountDetail.Industry,
							SecondIndustry:        accountDetail.SecondIndustry,
							Recharged:             libUtils.BoolParsInt(accountDetail.Recharged),
							CorporationName:       accountDetail.CorporationName,
							ReviewStatus:          accountDetail.ReviewStatus,
							FrozenStatus:          accountDetail.FrozenStatus,
							TransferAccountStatus: libUtils.BoolParsInt(accountDetail.TransferAccountStatus),
							ChildReviewStatusInfo: libUtils.AnyMarshalToString(accountDetail.ChildReviewStatusInfo),
							CopyAccount:           libUtils.BoolParsInt(accountDetail.CopyAccount),
							ReviewDetail:          libUtils.AnyMarshalToString(accountDetail.ReviewDetails),
							DirectRebate:          accountDetail.DirectRebate,
							OptimizerOwner:        accountDetail.OptimizerOwner,
							//AutoOut:              ,
							AccountAutoManage: 0, // 具体导入账户的时候再获取
							DayBudget:         0, // 具体导入账户的时候再获取
							Remark:            "",
							AuthSource:        "1",
							AuthStatus:        0, // 默认导入进来的都是未授权的
							DeliveryStatus:    0,
							Owner:             0,
							CreateTime:        accountDetail.CreateTime,
						})
						// 删除当前accountIds 元素
						accountIds = append(accountIds[:i], accountIds[i+1:]...)
						break
					}
					if len(accountIds) == 0 {
						break
					}
				}
				accountId = int(accountDetail.AccountID)
			}
			if lastAccountId < accountId {
				accountList = accountIds
				break
			}
			//err = s.BatchAdd(ctx, batchReq)
			//if err != nil {
			//	g.Log().Errorf(ctx, "sKsAdvertiserAccountInfo 批量新增失败%v", err.Error())
			//}
		}

	})
	return
}

func (s *sKsAdvertiserAccountInfo) Import(ctx context.Context, req *model.KsAdvertiserAccountInfoImportReq) (accountIds []int, err error) {
	err = g.Try(ctx, func(innerCtx context.Context) {
		if len(req.AccountIds) == 0 {
			return
		}
		sort.Ints(req.AccountIds)

		adList, ids, e := s.PullByAIds(ctx, req.AccountIds, req.AgentId, req.AuthorizeKsAccount)
		if e != nil {
			g.Log().Errorf(ctx, "Import error: %v", e)
			return
		}
		accountIds = ids
		if len(accountIds) > 0 {
			return
		}
		s.handleAdList(ctx, adList, req)
	})
	return
}

func (s *sKsAdvertiserAccountInfo) Import2(ctx context.Context, req *model.KsAdvertiserAccountInfoImportReq) (err error) {
	err = g.Try(ctx, func(innerCtx context.Context) {
		adList := make([]*model.KsAdvertiserAccountInfoAddReq, 0)
		_ = dao.KsAdvertiserAccountInfo.Ctx(ctx).WhereIn(
			dao.KsAdvertiserAccountInfo.Columns().AccountId, req.AccountIds,
		).Scan(&adList)
		s.handleAdList(ctx, adList, req)
	})
	return
}

type KsAdvertiserData struct {
	ImportReq *model.KsAdvertiserAccountInfoImportReq // 对应的导入请求
	AddList   []*model.KsAdvertiserAccountInfoAddReq  // 关联的广告列表
}

// SyncKsAccountUnitCampaign 同步快手账户计划数据
func (s *sKsAdvertiserAccountInfo) SyncKsAccountUnitCampaign(ctx context.Context) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var pageNo = 1
		var pageSize = 500
		for {
			// 获取账户列表
			advertiserList, _ := service.KsAdvertiserAccountInfo().GetAccountList(ctx, pageNo, pageSize)
			if len(advertiserList) == 0 {
				break
			}
			// 将advertiserList 构建成 handleAdList 能执行的参数
			handleAdList := make([]*KsAdvertiserData, 0)
			for _, item := range advertiserList {
				var have = false
				res := &model.KsAdvertiserAccountInfoAddReq{}
				_ = copier.Copy(res, item)
				for _, data := range handleAdList {
					if data.ImportReq.AuthorizeKsAccount == item.AuthorizeKsAccount && item.AgentAccountId == item.AgentAccountId {
						data.ImportReq.AccountIds = append(data.ImportReq.AccountIds, int(item.AccountId))
						data.AddList = append(data.AddList, res)
						have = true
					}
				}
				if !have {
					handleAdList = append(handleAdList, &KsAdvertiserData{
						ImportReq: &model.KsAdvertiserAccountInfoImportReq{
							AgentId:            item.AgentAccountId,
							AuthorizeKsAccount: item.AuthorizeKsAccount,
							Owner:              gconv.Int64(item.Owner),
							AccountIds:         []int{int(item.AccountId)},
						},
						AddList: []*model.KsAdvertiserAccountInfoAddReq{
							res,
						},
					})
				}
			}
			for _, data := range handleAdList {
				s.handleAdList(ctx, data.AddList, data.ImportReq)
			}
			pageNo++
		}
	})
	return
}

// 公共处理逻辑
func (s *sKsAdvertiserAccountInfo) handleAdList(ctx context.Context, adList []*model.KsAdvertiserAccountInfoAddReq, req *model.KsAdvertiserAccountInfoImportReq) {
	agentInfo, _ := service.KsAdvertiserAgentInfo().GetAPPConfig(ctx, req.AgentId, req.AuthorizeKsAccount)
	accessToken := ksApi.GetAccessTokenByAgentCache(req.AgentId, req.AuthorizeKsAccount, gconv.Int64(agentInfo.AppId), agentInfo.Secret)
	pool := libUtils.NewSafeGoPool(5)
	for _, addReq := range adList {
		addReq.Owner = int(req.Owner)
		addReq.AuthStatus = 1
		pool.SafeGo(func() {
			c, cancel := context.WithCancel(context.Background())
			defer cancel()
			_ = service.KsAdvertiserCampaign().PullCampaignByAdId(c, accessToken, addReq.AccountId)
			_ = service.KsAdvertiserUnit().PullUnitByAdId(c, accessToken, addReq.AccountId)
		})

		// 日预算
		if budget, err := ksApi.GetKSApiClient().QueryAccountBudgetService.AccessToken(accessToken).
			SetReq(ksApi.QueryAccountBudgetReq{AdvertiserId: gconv.Int64(addReq.AccountId)}).Do(); err != nil {
			g.Log().Errorf(ctx, "拉取账户日预算失败: %v", err)
		} else if budget != nil && budget.Data != nil {
			d := *budget.Data
			if len(d.DayBudgetSchedule) > 0 {
				addReq.DayBudget = d.DayBudgetSchedule[0]
			} else {
				addReq.DayBudget = d.DayBudget
			}
		}

		// 智投配置
		if autoInfo, _ := ksApi.GetKSApiClient().QueryAccountAutoInfoService.AccessToken(accessToken).
			SetReq(ksApi.QueryAccountAutoInfoReq{AdvertiserId: gconv.Int64(addReq.AccountId)}).Do(); autoInfo != nil && autoInfo.Data != nil {
			addReq.AccountAutoManage = autoInfo.Data.AccountAutoManage
		}

		// 余额
		if fund, _ := ksApi.GetKSApiClient().QueryAdvertiserFundService.AccessToken(accessToken).
			SetReq(ksApi.QueryAdvertiserFundReq{AdvertiserID: gconv.Int64(addReq.AccountId)}).Do(); fund != nil && fund.Data != nil {
			addReq.Balance = fund.Data.Balance
		}
	}
	pool.Wait()
	if err := s.BatchAdd(ctx, adList); err != nil {
		g.Log().Errorf(ctx, "BatchAdd error: %v", err)
	}
}

// BatchAdd
func (s *sKsAdvertiserAccountInfo) BatchAdd(ctx context.Context, batchReq []*model.KsAdvertiserAccountInfoAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserAccountInfo.Ctx(ctx).Save(batchReq)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserAccountInfo) Add(ctx context.Context, req *model.KsAdvertiserAccountInfoAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserAccountInfo.Ctx(ctx).Insert(do.KsAdvertiserAccountInfo{
			AccountId:             req.AccountId,
			AgentAccountId:        req.AgentAccountId,
			AuthorizeKsAccount:    req.AuthorizeKsAccount,
			UserId:                req.UserId,
			AccountName:           req.AccountName,
			ResponsiblePerson:     req.ResponsiblePerson,
			UcType:                req.UcType,
			PaymentType:           req.PaymentType,
			Balance:               req.Balance,
			CreditBalance:         req.CreditBalance,
			ExtendedBalance:       req.ExtendedBalance,
			Rebate:                req.Rebate,
			PreRebate:             req.PreRebate,
			ContractRebate:        req.ContractRebate,
			TotalBalance:          req.TotalBalance,
			LoLimit:               req.LoLimit,
			SingleOut:             req.SingleOut,
			AutoOut:               req.AutoOut,
			BalanceWarn:           req.BalanceWarn,
			ProductName:           req.ProductName,
			FirstCostDay:          req.FirstCostDay,
			Industry:              req.Industry,
			SecondIndustry:        req.SecondIndustry,
			Recharged:             req.Recharged,
			CorporationName:       req.CorporationName,
			ReviewStatus:          req.ReviewStatus,
			FrozenStatus:          req.FrozenStatus,
			TransferAccountStatus: req.TransferAccountStatus,
			ChildReviewStatusInfo: req.ChildReviewStatusInfo,
			CopyAccount:           req.CopyAccount,
			ReviewDetail:          req.ReviewDetail,
			DirectRebate:          req.DirectRebate,
			OptimizerOwner:        req.OptimizerOwner,
			AccountAutoManage:     req.AccountAutoManage,
			DayBudget:             req.DayBudget,
			Remark:                req.Remark,
			AuthSource:            req.AuthSource,
			AuthStatus:            req.AuthStatus,
			DeliveryStatus:        req.DeliveryStatus,
			Owner:                 req.Owner,
			DeliveryType:          req.DeliveryType,
			EffectFirst:           req.EffectFirst,
			CreateTime:            req.CreateTime,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserAccountInfo) Edit(ctx context.Context, req *model.KsAdvertiserAccountInfoEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accountInfo, err := s.GetByAccountId(ctx, req.AccountId)
		accountInfo.Remark = req.Remark
		accountInfo.Owner = req.Owner
		accountInfo.DeliveryStatus = req.DeliveryStatus
		_, err = dao.KsAdvertiserAccountInfo.Ctx(ctx).WherePri(req.AccountId).Update(accountInfo)
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserAccountInfo) Delete(ctx context.Context, accountIds []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserAccountInfo.Ctx(ctx).Delete(dao.KsAdvertiserAccountInfo.Columns().AccountId+" in (?)", accountIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sKsAdvertiserAccountInfo) GetAccessToken(ctx context.Context, advertiserId int64) (accessToken string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		ksAccountInfo, _ := s.GetByAccountId(ctx, gconv.Int64(advertiserId))
		agentInfo, _ := service.KsAdvertiserAgentInfo().GetAPPConfig(ctx, ksAccountInfo.AgentAccountId, ksAccountInfo.AuthorizeKsAccount)
		accessToken = ksApi.GetAccessTokenByAgentCache(ksAccountInfo.AgentAccountId, ksAccountInfo.AuthorizeKsAccount, gconv.Int64(agentInfo.AppId), agentInfo.Secret)
		if accessToken == "" {
			liberr.ErrIsNil(ctx, errors.New(commonConsts.ErrMsgGetAccessToken))
		}
	})
	return
}

func (s *sKsAdvertiserAccountInfo) GetAccountList(ctx context.Context, pageNo int, pageSize int) (res []*model.KsAdvertiserAccountInfoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = make([]*model.KsAdvertiserAccountInfoInfoRes, 0)
		err = dao.KsAdvertiserAccountInfo.Ctx(ctx).
			WhereGT(dao.KsAdvertiserAccountInfo.Columns().Owner, 0).
			Page(pageNo, pageSize).
			OrderDesc(dao.KsAdvertiserAccountInfo.Columns().AccountId).
			Scan(&res)
	})
	return
}

func (s *sKsAdvertiserAccountInfo) GetCorporationList(ctx context.Context, corporationName string, pageNo int, pageSize int) (listRes *model.GetCorporationListRes, err error) {
	listRes = new(model.GetCorporationListRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.KsAdvertiserAccountInfo.Ctx(ctx)
		if !admin && len(userIds) > 0 {
			m = m.WhereIn("owner", userIds)
		}
		if corporationName != "" {
			m = m.Where("corporation_name like ?", "%"+corporationName+"%")
		}
		listRes.Total, err = m.Group("corporation_name").Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if pageNo == 0 {
			pageNo = 1
		}
		listRes.CurrentPage = pageNo
		if pageSize == 0 {
			pageSize = consts.PageSize
		}
		err = m.Fields("corporation_name as corporationName").
			Group("corporation_name").
			Page(pageNo, pageSize).
			Scan(&listRes.List)
		liberr.ErrIsNil(ctx, err)
	})
	return
}

// GetAccountAutoInfo 查询账户智投配置信息
func (s *sKsAdvertiserAccountInfo) GetAccountAutoInfo(ctx context.Context, advertiserId int64) (res *model.GetAccountAutoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.GetAccountAutoInfoRes)
		accessToken, err1 := s.GetAccessToken(ctx, advertiserId)
		liberr.ErrIsNil(ctx, err1)
		accountAutoInfo, err2 := ksApi.GetKSApiClient().QueryAccountAutoInfoService.
			AccessToken(accessToken).
			SetReq(ksApi.QueryAccountAutoInfoReq{AdvertiserId: gconv.Int64(advertiserId)}).
			Do()
		liberr.ErrIsNil(ctx, err2)
		res.AccountSimpleQueryResp863Snake = accountAutoInfo.Data
	})
	return
}

// GetAccountIncExplore 查询账户增量探索配置
func (s *sKsAdvertiserAccountInfo) GetAccountIncExplore(ctx context.Context, advertiserId int64) (res *model.GetAccountIncExploreRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.GetAccountIncExploreRes)
		accessToken, err1 := s.GetAccessToken(ctx, advertiserId)
		liberr.ErrIsNil(ctx, err1)
		accountIncExploreRes, err2 := ksApi.GetKSApiClient().QueryAccountIncExploreService.AccessToken(accessToken).
			SetReq(ksApi.QueryAccountIncExploreReq{AdvertiserId: gconv.Int64(advertiserId)}).
			Do()
		liberr.ErrIsNil(ctx, err2, "查询增量探索配置失败")
		res.List = *accountIncExploreRes.Data
	})
	return
}

// AddAccountIncExplore 新增账户增量探索配置
func (s *sKsAdvertiserAccountInfo) AddAccountIncExplore(ctx context.Context, req *model.AddAccountIncExploreReq) (res *model.AddAccountIncExploreRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.AddAccountIncExploreRes)
		accessToken, err1 := s.GetAccessToken(ctx, req.AdvertiserId)
		liberr.ErrIsNil(ctx, err1)
		addIncExplore, err2 := ksApi.GetKSApiClient().AddAccountIncExploreService.
			AccessToken(accessToken).
			SetReq(ksApi.AddAccountIncExploreReq{
				AdvertiserId:   gconv.Int64(req.AdvertiserId),
				IncExploreInfo: req.IncExploreInfo,
			}).Do()
		liberr.ErrIsNil(ctx, err2)
		res.List = *addIncExplore.Data
	})
	return
}

// UpdateAccountIncExplore 编辑账户增量探索配置
func (s *sKsAdvertiserAccountInfo) UpdateAccountIncExplore(ctx context.Context, req *model.UpdateAccountIncExploreReq) (res *model.UpdateAccountIncExploreRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.UpdateAccountIncExploreRes)
		accessToken, err1 := s.GetAccessToken(ctx, req.AdvertiserId)
		liberr.ErrIsNil(ctx, err1)
		updateIncExplore, err2 := ksApi.GetKSApiClient().UpdateAccountIncExploreService.
			AccessToken(accessToken).
			SetReq(ksApi.UpdateAccountIncExploreReq{
				AdvertiserId:   gconv.Int64(req.AdvertiserId),
				IncExploreInfo: []ksApi.GwIncExploreDetailDto{req.GwIncExploreDetailDto},
			}).Do()
		liberr.ErrIsNil(ctx, err2)
		res.List = *updateIncExplore.Data
	})
	return
}

// DeleteAccountIncExplore 删除账户增量探索配置
func (s *sKsAdvertiserAccountInfo) DeleteAccountIncExplore(ctx context.Context, req *model.DeleteAccountIncExploreReq) (res *model.DeleteAccountIncExploreRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.DeleteAccountIncExploreRes)
		accessToken, err1 := s.GetAccessToken(ctx, req.AdvertiserId)
		liberr.ErrIsNil(ctx, err1)
		deleteIncExploreRes, err2 := ksApi.GetKSApiClient().DeleteAccountIncExploreService.AccessToken(accessToken).
			SetReq(ksApi.DeleteAccountIncExploreReq{
				AdvertiserId:       gconv.Int64(req.AdvertiserId),
				OcpxActionType:     req.OcpxActionType,
				DeepConversionType: req.DeepConversionType,
			}).
			Do()
		liberr.ErrIsNil(ctx, err2)
		res.List = *deleteIncExploreRes.Data
	})
	return
}

// PauseAccountIncExplore 暂停账户增量探索配置
func (s *sKsAdvertiserAccountInfo) PauseAccountIncExplore(ctx context.Context, req *model.PauseAccountIncExploreReq) (res *model.PauseAccountIncExploreRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.PauseAccountIncExploreRes)
		accessToken, err1 := s.GetAccessToken(ctx, req.AdvertiserId)
		liberr.ErrIsNil(ctx, err1)
		pauseIncExploreRes, err2 := ksApi.GetKSApiClient().PauseAccountIncExploreService.AccessToken(accessToken).
			SetReq(ksApi.PauseAccountIncExploreReq{
				AdvertiserId: gconv.Int64(req.AdvertiserId),
				PauseInfo:    []ksApi.GwIncExploreDetailPauseDto{req.GwIncExploreDetailPauseDto},
			}).Do()
		liberr.ErrIsNil(ctx, err2)
		res.List = *pauseIncExploreRes.Data
	})
	return
}

// RebootAccountIncExplore 重启账户增量探索配置
func (s *sKsAdvertiserAccountInfo) RebootAccountIncExplore(ctx context.Context, req *model.RebootAccountIncExploreReq) (res *model.RebootAccountIncExploreRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.RebootAccountIncExploreRes)
		accessToken, err1 := s.GetAccessToken(ctx, req.AdvertiserId)
		liberr.ErrIsNil(ctx, err1)
		rebootIncExploreRes, err2 := ksApi.GetKSApiClient().RebootAccountIncExploreService.AccessToken(accessToken).
			SetReq(ksApi.RebootAccountIncExploreReq{
				AdvertiserId: gconv.Int64(req.AdvertiserId),
				RebootInfo:   []ksApi.GwIncExploreDetailRebootDto{req.GwIncExploreDetailRebootDto},
			}).Do()
		liberr.ErrIsNil(ctx, err2)
		res.List = *rebootIncExploreRes.Data
	})
	return
}

// GetAccountBalance 实时查询账户余额
func (s *sKsAdvertiserAccountInfo) GetAccountBalance(ctx context.Context, advertiserIds []int64) (res *model.GetAccountBalanceRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.GetAccountBalanceRes)
		for _, advertiserId := range advertiserIds {
			accessToken, _ := s.GetAccessToken(ctx, advertiserId)
			if accessToken == "" {
				continue
			}
			accountBalanceRes, err1 := ksApi.GetKSApiClient().QueryAdvertiserFundService.AccessToken(accessToken).
				SetReq(ksApi.QueryAdvertiserFundReq{
					AdvertiserID: gconv.Int64(advertiserId),
				}).Do()
			if err1 != nil {
				g.Log().Error(ctx, "拉取快手账户%v余额失败 %v", advertiserId, err1.Error())
				continue
			}
			res.List = append(res.List, &model.GetAccountBalanceInfo{
				AdvertiserId:   advertiserId,
				AccountBalance: accountBalanceRes.Data.Balance,
			})
		}
	})
	return
}

func (s *sKsAdvertiserAccountInfo) UpdateAccountInfo(ctx context.Context, req *model.KsAdvertiserAccountInfoUpdateReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accountInfo := do.KsAdvertiserAccountInfo{
			UpdatedAt: gtime.Now(),
		}
		if req.AccountAutoManage != nil {
			accountInfo.AccountAutoManage = gconv.Int(req.AccountAutoManage)
		}
		if req.DayBudget != nil {
			accountInfo.DayBudget = gconv.Int64(req.DayBudget)
		}
		_, err = dao.KsAdvertiserAccountInfo.Ctx(ctx).WherePri(req.AccountId).Update(accountInfo)
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}
